
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from joblib import dump

# Load and prepare data
df = pd.read_csv("financial_training_data_updated.csv")

# Drop personal info
df = df.drop(columns=["ID_No", "Rank", "Name", "Unit"], errors="ignore")

# Split features and target
X = df.drop(columns=["Financial_Profile_Score"])
y = df["Financial_Profile_Score"]

# Encode categorical variables
for col in X.columns:
    if X[col].dtype == object:
        X[col], _ = pd.factorize(X[col])

# Train model
model = RandomForestClassifier(n_estimators=100, random_state=42)
model.fit(X, y)

# Save model
dump(model, "model.pkl")
